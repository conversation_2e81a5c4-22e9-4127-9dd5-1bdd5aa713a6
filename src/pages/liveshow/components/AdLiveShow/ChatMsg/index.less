@import (reference) '../../../assets/style-util.less';

.ad-chat-msg-wrapper {
    width: 100%;
    bottom: 144pr;
    height: 26vh;
    max-height: 630pr;
    padding: 0 30pr 0;
    color: #fff;
    overflow: hidden;
    mask: linear-gradient(transparent, #fff 15%);

    .last-msg {
        position: absolute;
        bottom: 9pr;
        left: 50%;
        display: flex;
        height: 92pr;
        padding: 27pr 51pr;
        font-size: 39pr;
        line-height: 1;
        text-align: center;
        color: #38f;
        border-radius: 100pr;
        background-color: #ecf4ff;
        transform: translateX(-50%);

        .msg-text {
            overflow: hidden;
            max-width: 500pr;
            max-width: 40vw;
            margin-left: 26pr;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .arrow {
            position: relative;
            top: 6pr;
            width: 24pr;
            height: 28pr;
            background: url(./img/arr.png) 0 0 no-repeat;
            background-size: 24pr 28pr;
        }

        &::after {
            content: '';
            height: 293% !important;

            .border-common-color(#38f, 100pr, all);
        }
    }

    .inner-wrapper {
        width: 100%;
        height: 100%;
        overflow: scroll;
        -webkit-overflow-scrolling: touch;

        &::-webkit-scrollbar {
            display: none;
            width: 0;
            height: 0;
        }

        .chat-list {
            min-height: 100%;
            margin-top: 150px;
        }

        .q2c-back-bg {
            background-color: #F356;
            border-radius: .14rem;
        }

        .q2c-chat-item-container {
            .chat-item {
                .author {
                    display: inline-block;
                    width: 40px;
                    height: 20px;
                    padding: 2.5px 6px;
                    border-radius: 8px;
                    color: #fff;
                    background-color: #F35;
                    font-size: 12px;
                    text-align: center;
                    line-height: 16px;
                    margin-right: 6px;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    box-sizing: border-box;
                }

                .user-name {
                    margin-right: 6px;
                }
            }
        }

        .chat-item-container {
            display: flex;
            align-items: center;
            margin-bottom: 12pr;

            .chat-item {
                padding: 12pr 24pr;
                font-weight: bold;
                font-size: 42pr;
                line-height: 66pr;
                .bgMask(.23, 42pr);

                .is-android & {
                    font-weight: normal;
                }

                .user-name {
                    color: #FFE187;
                }

                .msg {
                    text-shadow: 0 0 .5px rgba(0, 0, 0, .3);
                }

                .msg-hint {
                    opacity: .6;
                }
            }
        }
    }
}

.ad-chat-msg-wrapper-cny {
    height: 20vh;
}
