/* eslint-disable max-len */
/*
 * @file 任意门api
 * <AUTHOR>
 * @date 2025-01-10 17:01:23
 */
import {getUrlAllParam} from '@/pages/liveshow/utils/getUrlParam';
import {isIOS} from '@baidu/xbox-native/detect';
import {once} from 'lodash';
import {anyMountResult, isDevMockFlag} from 'mock/debug';
import {isDev} from '../utils/env';
import {sendMonitor} from '../monitor';
import {q2cTestUserstat} from '../monitor/abtest';
import {prefetchRequest, request} from './request';
import {getCuid, getNetwork, requestUa} from './utils';

export const dpSwitchMap = {
    open: 1,
    close: 0
};
function getAdUrlParams(urlParams = {}) {
    const {
        bd_bxst = '',
        bd_vid = '',
        ch = '',
        fid = '',
        ext_params = ''
    } = urlParams || {};
    return `ch=${ch}&bd_vid=${bd_vid}&bdvid=${bd_vid}&bd_bxst=${bd_bxst}&fid=${fid}${ext_params ? '&' : ''}${ext_params}`;
}
function getThirdUrlParams() {
    const {
        bd_vid = '',
        ext_params = ''
    } = urlParams || {};
    return `bdvid=${bd_vid}${ext_params ? '&' : ''}${ext_params}`;
}
// 页面初始化只执行一次
const getCachedUrlParams = once(getUrlAllParam);
const getCachedAdUrlParams = once(getAdUrlParams);
const getCachedThirdAdUrlParams = once(getThirdUrlParams);
const urlParams = getCachedUrlParams(location.href);
export const ad_url_params = getCachedAdUrlParams(urlParams);
export const ad_third_url_params = getCachedThirdAdUrlParams(urlParams);

export async function queryAntiCheats({roomId}) {
    const {
        bd_vid = ''
    } = urlParams || {};
    const params = {
        execTypes: 'queryAntiCheats',
        roomId: +roomId,
        extLog: JSON.stringify({
            bd_vid,
            ad_url_params,
            ad_third_url_params
        }),
        network: getNetwork(),
        _client_type: isIOS() ? 1 : 2 // 类型1-iphone;2-android
    };
    try {
        const res = await request({
            path: 'cluelivec/commonApi/v1.0/bizData/get',
            params
        });
        return res;
    }
    catch (error) {
        console.log(error);
        return {};
    }
}


export async function getRoomEnterInfo({uid, roomId}) {
    const {
        bd_vid = '',
        livesource = '',
        source = ''
    } = urlParams || {};
    const cuid = getCuid();
    const jsonParams = Object.fromEntries(new URLSearchParams(window.location.search).entries());
    const params = {
        execTypes: 'queryAnyMountData,queryDynamicPeopleData',
        roomId: +roomId,
        uid,
        cuid,
        ua: requestUa,
        timestamp: Date.now(),
        liveSource: livesource,
        ext: JSON.stringify({
            ext: {
                source: source,
                bd_vid
            }
        }),
        h5RoomUrlParams: JSON.stringify(jsonParams),
        extLog: JSON.stringify({
            bd_vid,
            ad_url_params,
            ad_third_url_params
        })
    };
    // 动态增强参数
    const mainRes = await prefetchRequest(window.__prefetchDatasource__?.requestMainData, () => {});
    const uk = mainRes?.host?.uk || '';
    params.anchorUk = uk;
    if (jsonParams.content_id) {
        params.execTypes += ',queryChatImData';
        // 先解析 ext，再加字段，再序列化回去
        const ext = JSON.parse(params.ext);
        ext.ext.sc_contentId = jsonParams.content_id;
        params.ext = JSON.stringify(ext);
    }
    const h5Sid = q2cTestUserstat();
    params.h5Sid = h5Sid;

    try {
        if (isDevMockFlag) {
            return anyMountResult;
        }
        const res = await request({
            path: 'cluelivec/commonApi/v1.0/bizData/get',
            params
        });
        return res;
    }
    catch (error) {
        console.log(error);
        return {};
    }
}

export async function requestRoomEnterInfoWithAntiCheats(roomId, uid) {
    const res = await prefetchRequest(
        window.__prefetchDatasource__?.requestRoomEnterInfoWithAntiCheats,
        () => sendSrcRequest({uid, roomId})
    );
    return res;
}
async function sendSrcRequest({uid, roomId}) {
    // 创建超时控制器和结果标记
    let isTimeout = false;
    const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
            isTimeout = true;
            reject(new Error('timeout'));
        }, 300);
    });

    try {
        // 竞速请求：反作弊检查 vs 超时控制
        const res = await Promise.race([
            queryAntiCheats({uid, roomId}),
            timeoutPromise
        ]);

        // 正常返回且未超时的情况
        if (res?.antiCheatsResult?.hitAnti) {
            sendMonitor('click', {level: 'live-anti_cheats_hit'});
            return {};
        }
        if (isDev) {
            console.warn('没命中反作弊');
        }
        return await getRoomEnterInfo({uid, roomId}); // 未命中继续请求
    }
    catch (error) {
        // 处理超时或其它错误
        if (isTimeout) {
            if (isDev) {
                console.warn('超时请求');
            }
            // 超时后发起正常请求
            return await getRoomEnterInfo({uid, roomId});
        }
        throw error; // 其它异常原样抛出
    }
}
