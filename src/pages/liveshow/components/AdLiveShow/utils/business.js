/*
 * @file 业务工具方法
 * <AUTHOR>
 * @date 2025-03-12 12:05:49
 */

export function showAnyDoor(anyMountResult) {
    return JSON.stringify(anyMountResult) !== '{}' && anyMountResult.anyMountSwitch === 1;
}

export function getTextByTransType(transType) {
    const textMap = {
        132: '获取专业解答，点此咨询！',
        152: '获取专业解答，点此咨询！',
        139: '获取专业解答，点此咨询！',
        153: '精彩内容，点此下载！',
        133: '加主播微信，领取资料！',
        131: '品质保障，点击购买！'
    };

    return textMap[transType] || '更多精彩内容，点击预约！';
}

export function getPeopleText(transType, room_id, liveCount) {
    const baseTextMap = {
        132: '人咨询',
        152: '人咨询',
        139: '人咨询',
        153: '人下载',
        133: '人添加主播微信',
        131: '人购买'
    };

    const fallbackText = '人预约';

    let actionText = baseTextMap[transType] || fallbackText;

    let countText = '多';
    if (typeof room_id === 'string' && /^\d+$/.test(room_id)) {
        const idStr = String(room_id);
        if (liveCount >= 10000 && idStr.length >= 4) {
            countText = idStr.slice(-4);
        }
        else if (liveCount >= 1000 && idStr.length >= 3) {
            countText = idStr.slice(-3);
        }
        else if (liveCount >= 100 && idStr.length >= 2) {
            countText = idStr.slice(-2);
        }
    }
    if (countText.startsWith('0')) {
        // 第一位改成5
        countText = '5' + countText.slice(1);
    }
    return `已有${countText}${actionText}`;
}
